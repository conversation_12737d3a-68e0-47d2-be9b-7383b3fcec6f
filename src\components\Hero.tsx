import { Button } from "@/components/ui/button";
import { useState, useEffect } from "react";
const Hero = () => {
  const [mousePosition, setMousePosition] = useState({
    x: 0,
    y: 0
  });
  const [isHovering, setIsHovering] = useState(false);
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      const heroSection = document.getElementById('hero-section');
      if (heroSection) {
        const rect = heroSection.getBoundingClientRect();
        setMousePosition({
          x: e.clientX - rect.left,
          y: e.clientY - rect.top
        });
      }
    };
    const heroSection = document.getElementById('hero-section');
    if (heroSection) {
      heroSection.addEventListener('mousemove', handleMouseMove);
      return () => heroSection.removeEventListener('mousemove', handleMouseMove);
    }
  }, []);
  return <section id="hero-section" onMouseEnter={() => setIsHovering(true)} onMouseLeave={() => setIsHovering(false)} className="w-full bg-transparent py-20 relative overflow-hidden lg:py-[90px]">
      {/* Mouse-following gradient cloud */}
      {isHovering && <div className="absolute w-96 h-96 bg-gradient-to-br from-lumen-yellow/20 to-lumen-yellow/5 rounded-full blur-3xl pointer-events-none transition-all duration-300 ease-out" style={{
      left: mousePosition.x - 192,
      top: mousePosition.y - 192
    }} />}
      
      <div className="max-w-content mx-auto px-6">
        <div className="flex items-center justify-center min-h-[60vh]">
          {/* Centered content */}
          <div className="space-y-10 relative z-10 text-center max-w-4xl">
            <div className="space-y-6">
              <div className="inline-block px-4 py-2 bg-lumen-yellow/20 rounded-full border border-lumen-yellow/30 backdrop-blur-sm">
                <span className="text-sm font-medium text-lumen-charcoal">🚀 Next-generation digital platforms</span>
              </div>
              <h1 className="text-5xl lg:text-6xl font-bold text-lumen-charcoal leading-tight xl:text-8xl">
                Built to last.<br />
                <span className="bg-gradient-to-r from-lumen-charcoal to-lumen-mid-gray bg-clip-text text-transparent">
                  Engineered for tomorrow.
                </span>
              </h1>
              <p className="text-xl lg:text-2xl text-lumen-mid-gray leading-relaxed max-w-3xl mx-auto">
                Digital platforms that stay online, stay fast, and stay secure—without the headaches.
              </p>
            </div>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" className="bg-gradient-to-r from-lumen-yellow to-lumen-yellow-hover hover:from-lumen-yellow-hover hover:to-lumen-yellow text-lumen-charcoal font-bold px-10 py-6 text-lg rounded-2xl shadow-xl hover:shadow-2xl transform hover:-translate-y-1 transition-all duration-300">
                Book a free health check
              </Button>
              <Button variant="outline" size="lg" className="border-2 border-lumen-yellow/30 hover:border-lumen-yellow text-lumen-charcoal font-bold px-8 py-6 text-lg rounded-2xl bg-white/50 backdrop-blur-sm hover:bg-lumen-yellow/10 transition-all duration-300">
                View our work
              </Button>
            </div>
          </div>
        </div>
      </div>
      
      {/* Floating elements */}
      <div className="absolute top-1/4 right-1/4 w-8 h-8 bg-gradient-to-br from-lumen-yellow to-lumen-yellow-hover rounded-full animate-float shadow-lg"></div>
      <div className="absolute bottom-1/3 left-1/4 w-6 h-6 bg-gradient-to-br from-lumen-yellow/70 to-lumen-yellow-hover/70 rounded-full animate-float delay-500 shadow-md"></div>
      <div className="absolute top-1/2 left-1/6 w-4 h-4 bg-gradient-to-br from-lumen-yellow/50 to-lumen-yellow-hover/50 rounded-full animate-float delay-1000 shadow-sm"></div>
    </section>;
};
export default Hero;