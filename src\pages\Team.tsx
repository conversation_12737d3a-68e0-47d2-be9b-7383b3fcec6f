
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { useState, useEffect } from "react";

const Team = () => {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [isHovering, setIsHovering] = useState(false);

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      const heroSection = document.getElementById('team-hero-section');
      if (heroSection) {
        const rect = heroSection.getBoundingClientRect();
        setMousePosition({
          x: e.clientX - rect.left,
          y: e.clientY - rect.top
        });
      }
    };

    const heroSection = document.getElementById('team-hero-section');
    if (heroSection) {
      heroSection.addEventListener('mousemove', handleMouseMove);
      return () => heroSection.removeEventListener('mousemove', handleMouseMove);
    }
  }, []);
  const teamMembers = [
    {
      name: "<PERSON>",
      role: "CEO & Founder",
      bio: "15 years of experience in enterprise technology solutions",
      image: "/placeholder.svg"
    },
    {
      name: "<PERSON>",
      role: "CT<PERSON>",
      bio: "Former Google engineer specializing in cloud architecture",
      image: "/placeholder.svg"
    },
    {
      name: "Emily Rodriguez",
      role: "Head of Security",
      bio: "Cybersecurity expert with government and Fortune 500 experience",
      image: "/placeholder.svg"
    },
    {
      name: "David Kim",
      role: "Lead Developer",
      bio: "Full-stack developer passionate about performance optimization",
      image: "/placeholder.svg"
    },
    {
      name: "Lisa Thompson",
      role: "Head of Client Success",
      bio: "Dedicated to ensuring client satisfaction and business growth",
      image: "/placeholder.svg"
    },
    {
      name: "James Wilson",
      role: "Infrastructure Specialist",
      bio: "Cloud infrastructure expert with 12+ years of experience",
      image: "/placeholder.svg"
    }
  ];

  return (
    <div className="min-h-screen bg-white">
      <Header />
      <main className="pt-16 lg:pt-20">
        <section
          id="team-hero-section"
          onMouseEnter={() => setIsHovering(true)}
          onMouseLeave={() => setIsHovering(false)}
          className="w-full bg-gradient-to-br from-lumen-yellow via-lumen-yellow/95 to-lumen-yellow-hover py-20 lg:py-28 pt-28 relative overflow-hidden"
        >
          {/* Mouse-following gradient cloud */}
          {isHovering && (
            <div
              className="absolute w-96 h-96 bg-gradient-to-br from-white/30 to-lumen-yellow-hover/20 rounded-full blur-3xl pointer-events-none transition-all duration-500 ease-out"
              style={{
                left: mousePosition.x - 192,
                top: mousePosition.y - 192
              }}
            />
          )}

          
          {/* Background pattern overlay */}
          <div className="absolute inset-0 bg-gradient-to-br from-transparent via-lumen-yellow/10 to-lumen-yellow-hover/20"></div>

          <div className="max-w-content mx-auto px-6 relative z-10">
            <div className="text-center mb-8">
              <div className="inline-block px-4 py-2 bg-white/20 rounded-full border border-white/30 backdrop-blur-sm mb-6">
                <span className="text-sm font-medium text-lumen-charcoal">👥 Meet the experts behind the magic</span>
              </div>
              <h2 className="text-4xl lg:text-5xl font-bold leading-tight xl:text-6xl mb-6">
                <span className="text-lumen-charcoal">Our team</span>
                
              </h2>
              <p className="text-lg text-lumen-charcoal/80 max-w-2xl mx-auto leading-relaxed">
                Meet the experts who make your technology challenges disappear.
              </p>
            </div>
          </div>
        </section>
          <div className="py-20 lg:py-28 pt-20">
        <div className="max-w-content mx-auto px-6">


          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-12">
            {teamMembers.map((member, index) => (
              <div key={index} className="text-center group">
                <div className="relative mb-6">
                  <div className="w-48 h-48 mx-auto rounded-full bg-gradient-to-br from-lumen-yellow/20 to-lumen-yellow/10 flex items-center justify-center group-hover:scale-105 transition-transform duration-300">
                    <img
                      src={member.image}
                      alt={member.name}
                      className="w-40 h-40 rounded-full object-cover"
                    />
                  </div>
                </div>
                <h3 className="text-xl font-bold text-lumen-charcoal mb-2">{member.name}</h3>
                <p className="text-lumen-yellow-hover font-medium mb-3">{member.role}</p>
                <p className="text-lumen-mid-gray text-sm leading-relaxed">{member.bio}</p>
              </div>
            ))}
          </div>
        </div>
      </div>
      </main>

      <Footer />
    </div>
  );
};

export default Team;
