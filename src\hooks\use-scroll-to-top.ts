import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';

/**
 * Custom hook that automatically scrolls to the top of the page when navigating
 * between routes, unless the URL contains an anchor hash.
 * 
 * When there is an anchor hash, it will attempt to scroll to that element instead.
 */
export const useScrollToTop = () => {
  const location = useLocation();

  useEffect(() => {
    // Check if the URL has a hash (anchor)
    if (location.hash) {
      // Try to scroll to the anchor element
      const element = document.querySelector(location.hash);
      if (element) {
        element.scrollIntoView({ behavior: 'smooth' });
      }
    } else {
      // No anchor, scroll to top
      window.scrollTo({ top: 0, left: 0, behavior: 'smooth' });
    }
  }, [location.pathname, location.hash]);
};
