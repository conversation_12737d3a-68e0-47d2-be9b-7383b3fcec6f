
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { useScrollToTop } from "@/hooks/use-scroll-to-top";
import Index from "./pages/Index";
import NotFound from "./pages/NotFound";
import WebDigital from "./pages/services/WebDigital";
import CloudInfrastructure from "./pages/services/CloudInfrastructure";
import EmailCommunication from "./pages/services/EmailCommunication";
import InternalSoftware from "./pages/services/InternalSoftware";
import SecurityCompliance from "./pages/services/SecurityCompliance";
import TechConsulting from "./pages/services/TechConsulting";
import SupportMaintenance from "./pages/services/SupportMaintenance";
import Products from "./pages/Products";
import Cases from "./pages/Cases";
import Team from "./pages/Team";
import Contact from "./pages/Contact";
import TermsConditions from "./pages/TermsConditions";
import PrivacyPolicy from "./pages/PrivacyPolicy";
import CookiePolicy from "./pages/CookiePolicy";

const queryClient = new QueryClient();

// Component to handle scroll-to-top functionality
const ScrollToTop = () => {
  useScrollToTop();
  return null;
};

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <BrowserRouter>
        <ScrollToTop />
        <Routes>
          <Route path="/" element={<Index />} />
          <Route path="/services/web-digital" element={<WebDigital />} />
          <Route path="/services/cloud-infrastructure" element={<CloudInfrastructure />} />
          <Route path="/services/email-communication" element={<EmailCommunication />} />
          <Route path="/services/internal-software" element={<InternalSoftware />} />
          <Route path="/services/security-compliance" element={<SecurityCompliance />} />
          <Route path="/services/tech-consulting" element={<TechConsulting />} />
          <Route path="/services/support-maintenance" element={<SupportMaintenance />} />
          <Route path="/products" element={<Products />} />
          <Route path="/cases" element={<Cases />} />
          <Route path="/team" element={<Team />} />
          <Route path="/contact" element={<Contact />} />
          <Route path="/terms" element={<TermsConditions />} />
          <Route path="/privacy" element={<PrivacyPolicy />} />
          <Route path="/cookies" element={<CookiePolicy />} />
          {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
          <Route path="*" element={<NotFound />} />
        </Routes>
      </BrowserRouter>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
