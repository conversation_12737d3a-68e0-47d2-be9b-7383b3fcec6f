import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { useState, useEffect } from "react";

const TermsConditions = () => {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [isHovering, setIsHovering] = useState(false);

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      const heroSection = document.getElementById('terms-hero-section');
      if (heroSection) {
        const rect = heroSection.getBoundingClientRect();
        setMousePosition({
          x: e.clientX - rect.left,
          y: e.clientY - rect.top
        });
      }
    };

    const heroSection = document.getElementById('terms-hero-section');
    if (heroSection) {
      heroSection.addEventListener('mousemove', handleMouseMove);
      return () => heroSection.removeEventListener('mousemove', handleMouseMove);
    }
  }, []);

  return (
    <div className="min-h-screen bg-white">
      <Header />
      <main className="pt-16 lg:pt-20">
        <section
          id="terms-hero-section"
          onMouseEnter={() => setIsHovering(true)}
          onMouseLeave={() => setIsHovering(false)}
          className="w-full bg-gradient-to-br from-lumen-yellow via-lumen-yellow/95 to-lumen-yellow-hover py-20 lg:py-28 pt-28 relative overflow-hidden"
        >
          {/* Mouse-following gradient cloud */}
          {isHovering && (
            <div
              className="absolute w-96 h-96 bg-gradient-to-br from-white/30 to-lumen-yellow-hover/20 rounded-full blur-3xl pointer-events-none transition-all duration-500 ease-out"
              style={{
                left: mousePosition.x - 192,
                top: mousePosition.y - 192
              }}
            />
          )}

          {/* Background pattern overlay */}
          <div className="absolute inset-0 bg-gradient-to-br from-transparent via-lumen-yellow/10 to-lumen-yellow-hover/20"></div>

          <div className="max-w-content mx-auto px-6 relative z-10">
            <div className="text-center mb-8">
              <div className="inline-block px-4 py-2 bg-white/20 rounded-full border border-white/30 backdrop-blur-sm mb-6">
                <span className="text-sm font-medium text-lumen-charcoal">📋 Legal Information</span>
              </div>
              <h2 className="text-4xl lg:text-5xl font-bold leading-tight xl:text-6xl mb-6">
                <span className="text-lumen-charcoal">Terms & </span>
                <span className="bg-gradient-to-r from-lumen-charcoal to-lumen-charcoal bg-clip-text text-transparent">
                  Conditions
                </span>
              </h2>
              <p className="text-lg text-lumen-charcoal/80 max-w-2xl mx-auto leading-relaxed">
                Please read these terms and conditions carefully before using our services.
              </p>
            </div>
          </div>
        </section>

        <div className="py-20 lg:py-28 pt-20">
          <div className="max-w-4xl mx-auto px-6">
            <div className="prose prose-lg max-w-none">
              <div className="bg-white rounded-xl p-8 border border-lumen-yellow/20 shadow-lg space-y-8">
                
                <div>
                  <h3 className="text-2xl font-bold text-lumen-charcoal mb-4">1. Acceptance of Terms</h3>
                  <p className="text-lumen-mid-gray leading-relaxed">
                    By accessing and using LumenWorks services, you accept and agree to be bound by the terms and provision of this agreement.
                  </p>
                </div>

                <div>
                  <h3 className="text-2xl font-bold text-lumen-charcoal mb-4">2. Services</h3>
                  <p className="text-lumen-mid-gray leading-relaxed">
                    LumenWorks provides technology consulting, software development, cloud infrastructure, and related digital services. We reserve the right to modify or discontinue services at any time.
                  </p>
                </div>

                <div>
                  <h3 className="text-2xl font-bold text-lumen-charcoal mb-4">3. User Responsibilities</h3>
                  <p className="text-lumen-mid-gray leading-relaxed">
                    You are responsible for maintaining the confidentiality of your account information and for all activities that occur under your account. You agree to notify us immediately of any unauthorized use.
                  </p>
                </div>

                <div>
                  <h3 className="text-2xl font-bold text-lumen-charcoal mb-4">4. Intellectual Property</h3>
                  <p className="text-lumen-mid-gray leading-relaxed">
                    All content, features, and functionality of our services are owned by LumenWorks and are protected by copyright, trademark, and other intellectual property laws.
                  </p>
                </div>

                <div>
                  <h3 className="text-2xl font-bold text-lumen-charcoal mb-4">5. Limitation of Liability</h3>
                  <p className="text-lumen-mid-gray leading-relaxed">
                    LumenWorks shall not be liable for any indirect, incidental, special, consequential, or punitive damages resulting from your use of our services.
                  </p>
                </div>

                <div>
                  <h3 className="text-2xl font-bold text-lumen-charcoal mb-4">6. Governing Law</h3>
                  <p className="text-lumen-mid-gray leading-relaxed">
                    These terms shall be governed by and construed in accordance with the laws of the jurisdiction in which LumenWorks operates.
                  </p>
                </div>

                <div>
                  <h3 className="text-2xl font-bold text-lumen-charcoal mb-4">7. Contact Information</h3>
                  <p className="text-lumen-mid-gray leading-relaxed">
                    If you have any questions about these Terms and Conditions, please contact <NAME_EMAIL>.
                  </p>
                </div>

                <div className="border-t border-lumen-yellow/20 pt-6">
                  <p className="text-sm text-lumen-mid-gray">
                    Last updated: January 2025
                  </p>
                </div>

              </div>
            </div>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default TermsConditions;
