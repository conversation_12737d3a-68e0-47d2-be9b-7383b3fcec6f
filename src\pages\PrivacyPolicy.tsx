import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { useState, useEffect } from "react";

const PrivacyPolicy = () => {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [isHovering, setIsHovering] = useState(false);

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      const heroSection = document.getElementById('privacy-hero-section');
      if (heroSection) {
        const rect = heroSection.getBoundingClientRect();
        setMousePosition({
          x: e.clientX - rect.left,
          y: e.clientY - rect.top
        });
      }
    };

    const heroSection = document.getElementById('privacy-hero-section');
    if (heroSection) {
      heroSection.addEventListener('mousemove', handleMouseMove);
      return () => heroSection.removeEventListener('mousemove', handleMouseMove);
    }
  }, []);

  return (
    <div className="min-h-screen bg-white">
      <Header />
      <main className="pt-16 lg:pt-20">
        <section
          id="privacy-hero-section"
          onMouseEnter={() => setIsHovering(true)}
          onMouseLeave={() => setIsHovering(false)}
          className="w-full bg-gradient-to-br from-lumen-yellow via-lumen-yellow/95 to-lumen-yellow-hover py-20 lg:py-28 pt-28 relative overflow-hidden"
        >
          {/* Mouse-following gradient cloud */}
          {isHovering && (
            <div
              className="absolute w-96 h-96 bg-gradient-to-br from-white/30 to-lumen-yellow-hover/20 rounded-full blur-3xl pointer-events-none transition-all duration-500 ease-out"
              style={{
                left: mousePosition.x - 192,
                top: mousePosition.y - 192
              }}
            />
          )}

          {/* Background pattern overlay */}
          <div className="absolute inset-0 bg-gradient-to-br from-transparent via-lumen-yellow/10 to-lumen-yellow-hover/20"></div>

          <div className="max-w-content mx-auto px-6 relative z-10">
            <div className="text-center mb-8">
              <div className="inline-block px-4 py-2 bg-white/20 rounded-full border border-white/30 backdrop-blur-sm mb-6">
                <span className="text-sm font-medium text-lumen-charcoal">🔒 Your Privacy Matters</span>
              </div>
              <h2 className="text-4xl lg:text-5xl font-bold leading-tight xl:text-6xl mb-6">
                <span className="text-lumen-charcoal">Privacy </span>
                <span className="bg-gradient-to-r from-lumen-charcoal to-lumen-charcoal bg-clip-text text-transparent">
                  Policy
                </span>
              </h2>
              <p className="text-lg text-lumen-charcoal/80 max-w-2xl mx-auto leading-relaxed">
                Learn how we collect, use, and protect your personal information.
              </p>
            </div>
          </div>
        </section>

        <div className="py-20 lg:py-28 pt-20">
          <div className="max-w-4xl mx-auto px-6">
            <div className="prose prose-lg max-w-none">
              <div className="bg-white rounded-xl p-8 border border-lumen-yellow/20 shadow-lg space-y-8">
                
                <div>
                  <h3 className="text-2xl font-bold text-lumen-charcoal mb-4">1. Information We Collect</h3>
                  <p className="text-lumen-mid-gray leading-relaxed">
                    We collect information you provide directly to us, such as when you create an account, contact us, or use our services. This may include your name, email address, phone number, and company information.
                  </p>
                </div>

                <div>
                  <h3 className="text-2xl font-bold text-lumen-charcoal mb-4">2. How We Use Your Information</h3>
                  <p className="text-lumen-mid-gray leading-relaxed">
                    We use the information we collect to provide, maintain, and improve our services, communicate with you, and comply with legal obligations. We do not sell your personal information to third parties.
                  </p>
                </div>

                <div>
                  <h3 className="text-2xl font-bold text-lumen-charcoal mb-4">3. Information Sharing</h3>
                  <p className="text-lumen-mid-gray leading-relaxed">
                    We may share your information with service providers who assist us in operating our business, when required by law, or with your consent. We require these parties to protect your information.
                  </p>
                </div>

                <div>
                  <h3 className="text-2xl font-bold text-lumen-charcoal mb-4">4. Data Security</h3>
                  <p className="text-lumen-mid-gray leading-relaxed">
                    We implement appropriate technical and organizational measures to protect your personal information against unauthorized access, alteration, disclosure, or destruction.
                  </p>
                </div>

                <div>
                  <h3 className="text-2xl font-bold text-lumen-charcoal mb-4">5. Your Rights</h3>
                  <p className="text-lumen-mid-gray leading-relaxed">
                    You have the right to access, update, or delete your personal information. You may also opt out of certain communications from us. Contact us to exercise these rights.
                  </p>
                </div>

                <div>
                  <h3 className="text-2xl font-bold text-lumen-charcoal mb-4">6. Cookies and Tracking</h3>
                  <p className="text-lumen-mid-gray leading-relaxed">
                    We use cookies and similar technologies to enhance your experience on our website. You can control cookie settings through your browser preferences.
                  </p>
                </div>

                <div>
                  <h3 className="text-2xl font-bold text-lumen-charcoal mb-4">7. Contact Us</h3>
                  <p className="text-lumen-mid-gray leading-relaxed">
                    If you have any questions about this Privacy Policy, please contact <NAME_EMAIL>.
                  </p>
                </div>

                <div className="border-t border-lumen-yellow/20 pt-6">
                  <p className="text-sm text-lumen-mid-gray">
                    Last updated: January 2025
                  </p>
                </div>

              </div>
            </div>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default PrivacyPolicy;
