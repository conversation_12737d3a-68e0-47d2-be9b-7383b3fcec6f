import { useLocation, Link } from "react-router-dom";
import { useEffect } from "react";
import Header from "@/components/Header";
import Footer from "@/components/Footer";

const NotFound = () => {
  const location = useLocation();

  useEffect(() => {
    console.error(
      "404 Error: User attempted to access non-existent route:",
      location.pathname
    );
  }, [location.pathname]);

  return (
    <div className="min-h-screen bg-white">
      {/* <Header /> */}
      <div className="flex items-center justify-center bg-gradient-to-br from-lumen-off-white via-white to-lumen-yellow/5 pt-20 pb-20">
        <div className="text-center max-w-md mx-auto px-6">
          <h1 className="text-6xl font-bold text-lumen-charcoal mb-4">404</h1>
          <p className="text-xl text-lumen-mid-gray mb-8">Oops! Page not found</p>
          <p className="text-lumen-mid-gray mb-8">The page you're looking for doesn't exist or has been moved.</p>
          <Link
            to="/"
            className="inline-block bg-lumen-yellow hover:bg-lumen-yellow-hover text-lumen-charcoal font-bold px-8 py-3 rounded-lg transition-colors"
          >
            Return to Home
          </Link>
        </div>
      </div>
      {/* <Footer /> */}
    </div>
  );
};

export default NotFound;
