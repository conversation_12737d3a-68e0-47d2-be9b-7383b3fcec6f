
import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

const CTAStripe = () => {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    companySize: ""
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log("Form submitted:", formData);
    // Handle form submission here
  };

  return (
    <section className="w-full bg-lumen-yellow py-16 lg:py-20">
      <div className="max-w-content mx-auto px-6">
        <div className="text-center mb-8">
          <h2 className="text-3xl lg:text-4xl font-bold text-lumen-charcoal mb-4">
            Get your free tech health check
          </h2>
          <p className="text-lg text-lumen-charcoal/80 max-w-2xl mx-auto">
            15-minute call to identify what's slowing down your business and how to fix it. No sales pitch, just actionable insights.
          </p>
        </div>
        
        <div className="max-w-lg mx-auto">
          <form onSubmit={handleSubmit} className="space-y-4">
            <Input
              type="text"
              placeholder="Your name"
              value={formData.name}
              onChange={(e) => setFormData({...formData, name: e.target.value})}
              className="bg-white border-lumen-charcoal/20 text-lumen-charcoal placeholder:text-lumen-mid-gray"
              required
            />
            
            <Input
              type="email"
              placeholder="Email address"
              value={formData.email}
              onChange={(e) => setFormData({...formData, email: e.target.value})}
              className="bg-white border-lumen-charcoal/20 text-lumen-charcoal placeholder:text-lumen-mid-gray"
              required
            />
            
            <Select onValueChange={(value) => setFormData({...formData, companySize: value})}>
              <SelectTrigger className="bg-white border-lumen-charcoal/20 text-lumen-charcoal">
                <SelectValue placeholder="Company size" />
              </SelectTrigger>
              <SelectContent className="bg-white">
                <SelectItem value="1-10">1-10 employees</SelectItem>
                <SelectItem value="11-50">11-50 employees</SelectItem>
                <SelectItem value="51-200">51-200 employees</SelectItem>
                <SelectItem value="201+">201+ employees</SelectItem>
              </SelectContent>
            </Select>
            
            <Button 
              type="submit"
              className="w-full bg-lumen-charcoal hover:bg-lumen-charcoal/90 text-white font-bold py-3"
            >
              Book your free health check
            </Button>
          </form>
          
          <p className="text-sm text-lumen-charcoal/70 text-center mt-4">
            No spam. We'll only contact you to schedule your health check.
          </p>
        </div>
      </div>
    </section>
  );
};

export default CTAStripe;
