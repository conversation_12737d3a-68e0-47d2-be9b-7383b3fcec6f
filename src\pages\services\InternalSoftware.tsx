
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { CheckCircle } from "lucide-react";

const InternalSoftware = () => {
  const features = [
    "Custom automation tools for repetitive tasks",
    "Integration with existing business systems",
    "User-friendly interfaces your team will love",
    "Scalable solutions that grow with you",
    "Training and documentation included",
    "Ongoing support and feature updates"
  ];

  const benefits = [
    "Minimum 30% reduction in manual tasks",
    "Improved accuracy and reduced human error",
    "Better data insights and reporting",
    "Streamlined workflows across departments",
    "ROI typically achieved within 6 months"
  ];

  return (
    <div className="min-h-screen bg-white">
      <Header />
      {/* Hero Section */}
      <section className="py-20 lg:py-28 pt-20 bg-gradient-to-br from-lumen-off-white via-white to-lumen-yellow/5">
        <div className="max-w-content mx-auto px-6 text-center">
          <div className="text-6xl mb-6">⚡</div>
          <h1 className="text-4xl lg:text-5xl font-bold text-lumen-charcoal mb-4">
            Internal Software & Automation
          </h1>
          <p className="text-xl text-lumen-mid-gray mb-6 max-w-2xl mx-auto">
            Custom tools that eliminate repetitive work and streamline your operations
          </p>
          <div className="inline-block px-4 py-2 bg-lumen-yellow/20 rounded-full border border-lumen-yellow/30 backdrop-blur-sm mb-8">
            <span className="text-sm font-medium text-lumen-charcoal">Tasks cut ≥ 30%</span>
          </div>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" className="bg-lumen-yellow hover:bg-lumen-yellow-hover text-lumen-charcoal font-bold px-10 py-6 text-lg">
              Book health check Today
            </Button>
            <Button variant="outline" size="lg" className="border-2 border-lumen-yellow/30 hover:border-lumen-yellow text-lumen-charcoal font-bold px-8 py-6 text-lg">
              Book Free Consultation
            </Button>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-white">
        <div className="max-w-content mx-auto px-6">
          <div className="grid lg:grid-cols-2 gap-16 items-center">
            <div>
              <h2 className="text-3xl font-bold text-lumen-charcoal mb-6">
                What you get
              </h2>
              <p className="text-lg text-lumen-mid-gray mb-8">Transform your business processes with custom software solutions. We build internal tools that automate workflows, integrate systems, and give your team more time for high-value work.</p>
              <ul className="space-y-4">
                {features.map((feature, index) => (
                  <li key={index} className="flex items-start space-x-3">
                    <CheckCircle className="h-6 w-6 text-lumen-yellow flex-shrink-0 mt-0.5" />
                    <span className="text-lumen-charcoal">{feature}</span>
                  </li>
                ))}
              </ul>
            </div>
            <div>
              <h2 className="text-3xl font-bold text-lumen-charcoal mb-6">
                Why it matters
              </h2>
              <ul className="space-y-4">
                {benefits.map((benefit, index) => (
                  <li key={index} className="flex items-start space-x-3">
                    <CheckCircle className="h-6 w-6 text-lumen-yellow flex-shrink-0 mt-0.5" />
                    <span className="text-lumen-charcoal">{benefit}</span>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-lumen-yellow/10 to-lumen-yellow/5">
        <div className="max-w-content mx-auto px-6 text-center">
          <h2 className="text-3xl lg:text-4xl font-bold text-lumen-charcoal mb-6">
            Ready to Book health check?
          </h2>
          <p className="text-lg text-lumen-mid-gray mb-8 max-w-2xl mx-auto">
            Let's discuss how we can help streamline your internal software & automation needs.
          </p>
          <Button size="lg" className="bg-lumen-yellow hover:bg-lumen-yellow-hover text-lumen-charcoal font-bold px-12 py-6 text-lg">
            Book Your Free Consultation
          </Button>
        </div>
      </section>
      <Footer />
    </div>
  );
};

export default InternalSoftware;
