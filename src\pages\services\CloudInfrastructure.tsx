import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { But<PERSON> } from "@/components/ui/button";
import { CheckCircle } from "lucide-react";

const CloudInfrastructure = () => {
  const features = [
    "Auto-scaling servers that grow with your business",
    "Automated backups every 15 minutes",
    "99.99% uptime with redundant systems",
    "Global CDN for worldwide performance",
    "24/7 monitoring and alerting",
    "Disaster recovery planning included",
  ];

  const benefits = [
    "Scalable infrastructure that adapts to demand",
    "Cost-effective solutions with transparent pricing",
    "Enterprise-grade security and compliance",
    "Expert management so you can focus on business",
    "Proven reliability with Fortune 500 companies",
  ];

  return (
    <div className="min-h-screen bg-white">
      <Header />
      {/* Hero Section */}
      <section className="py-20 lg:py-28 pt-20 bg-gradient-to-br from-lumen-off-white via-white to-lumen-yellow/5">
        <div className="max-w-content mx-auto px-6 text-center">
          <div className="text-6xl mb-6">☁️</div>
          <h1 className="text-4xl lg:text-5xl font-bold text-lumen-charcoal mb-4">
            Cloud Infrastructure
          </h1>
          <p className="text-xl text-lumen-mid-gray mb-6 max-w-2xl mx-auto">
            Servers that grow with your business and protect your data
            automatically
          </p>
          <div className="inline-block px-4 py-2 bg-lumen-yellow/20 rounded-full border border-lumen-yellow/30 backdrop-blur-sm mb-8">
            <span className="text-sm font-medium text-lumen-charcoal">
              Autoscale 40s, backups every 15 min
            </span>
          </div>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button
              size="lg"
              className="bg-lumen-yellow hover:bg-lumen-yellow-hover text-lumen-charcoal font-bold px-10 py-6 text-lg"
            >
              Book health check Today
            </Button>
            <Button
              variant="outline"
              size="lg"
              className="border-2 border-lumen-yellow/30 hover:border-lumen-yellow text-lumen-charcoal font-bold px-8 py-6 text-lg"
            >
              Book Free Consultation
            </Button>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-white">
        <div className="max-w-content mx-auto px-6">
          <div className="grid lg:grid-cols-2 gap-16 items-center">
            <div>
              <h2 className="text-3xl font-bold text-lumen-charcoal mb-6">
                What you get
              </h2>
              <p className="text-lg text-lumen-mid-gray mb-8">
                Our cloud infrastructure solutions ensure your applications stay
                online and perform optimally, no matter how fast you grow or how
                much traffic you receive.
              </p>
              <ul className="space-y-4">
                {features.map((feature, index) => (
                  <li key={index} className="flex items-start space-x-3">
                    <CheckCircle className="h-6 w-6 text-lumen-yellow flex-shrink-0 mt-0.5" />
                    <span className="text-lumen-charcoal">{feature}</span>
                  </li>
                ))}
              </ul>
            </div>
            <div>
              <h2 className="text-3xl font-bold text-lumen-charcoal mb-6">
                Why it matters
              </h2>
              <ul className="space-y-4">
                {benefits.map((benefit, index) => (
                  <li key={index} className="flex items-start space-x-3">
                    <CheckCircle className="h-6 w-6 text-lumen-yellow flex-shrink-0 mt-0.5" />
                    <span className="text-lumen-charcoal">{benefit}</span>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-lumen-yellow/10 to-lumen-yellow/5">
        <div className="max-w-content mx-auto px-6 text-center">
          <h2 className="text-3xl lg:text-4xl font-bold text-lumen-charcoal mb-6">
            Ready to Book health check?
          </h2>
          <p className="text-lg text-lumen-mid-gray mb-8 max-w-2xl mx-auto">
            Let's discuss how we can help streamline your cloud infrastructure
            needs.
          </p>
          <Button
            size="lg"
            className="bg-lumen-yellow hover:bg-lumen-yellow-hover text-lumen-charcoal font-bold px-12 py-6 text-lg"
          >
            Book Your Free Consultation
          </Button>
        </div>
      </section>
      <Footer />
    </div>
  );
};

export default CloudInfrastructure;
