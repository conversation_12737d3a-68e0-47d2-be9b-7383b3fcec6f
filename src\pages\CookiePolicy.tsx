import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { useState, useEffect } from "react";

const CookiePolicy = () => {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [isHovering, setIsHovering] = useState(false);

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      const heroSection = document.getElementById('cookie-hero-section');
      if (heroSection) {
        const rect = heroSection.getBoundingClientRect();
        setMousePosition({
          x: e.clientX - rect.left,
          y: e.clientY - rect.top
        });
      }
    };

    const heroSection = document.getElementById('cookie-hero-section');
    if (heroSection) {
      heroSection.addEventListener('mousemove', handleMouseMove);
      return () => heroSection.removeEventListener('mousemove', handleMouseMove);
    }
  }, []);

  return (
    <div className="min-h-screen bg-white">
      <Header />
      <main className="pt-16 lg:pt-20">
        <section
          id="cookie-hero-section"
          onMouseEnter={() => setIsHovering(true)}
          onMouseLeave={() => setIsHovering(false)}
          className="w-full bg-gradient-to-br from-lumen-yellow via-lumen-yellow/95 to-lumen-yellow-hover py-20 lg:py-28 pt-28 relative overflow-hidden"
        >
          {/* Mouse-following gradient cloud */}
          {isHovering && (
            <div
              className="absolute w-96 h-96 bg-gradient-to-br from-white/30 to-lumen-yellow-hover/20 rounded-full blur-3xl pointer-events-none transition-all duration-500 ease-out"
              style={{
                left: mousePosition.x - 192,
                top: mousePosition.y - 192
              }}
            />
          )}

          {/* Background pattern overlay */}
          <div className="absolute inset-0 bg-gradient-to-br from-transparent via-lumen-yellow/10 to-lumen-yellow-hover/20"></div>

          <div className="max-w-content mx-auto px-6 relative z-10">
            <div className="text-center mb-8">
              <div className="inline-block px-4 py-2 bg-white/20 rounded-full border border-white/30 backdrop-blur-sm mb-6">
                <span className="text-sm font-medium text-lumen-charcoal">🍪 Cookie Information</span>
              </div>
              <h2 className="text-4xl lg:text-5xl font-bold leading-tight xl:text-6xl mb-6">
                <span className="text-lumen-charcoal">Cookie </span>
                <span className="bg-gradient-to-r from-lumen-charcoal to-lumen-charcoal bg-clip-text text-transparent">
                  Policy
                </span>
              </h2>
              <p className="text-lg text-lumen-charcoal/80 max-w-2xl mx-auto leading-relaxed">
                Learn about how we use cookies and similar technologies on our website.
              </p>
            </div>
          </div>
        </section>

        <div className="py-20 lg:py-28 pt-20">
          <div className="max-w-4xl mx-auto px-6">
            <div className="prose prose-lg max-w-none">
              <div className="bg-white rounded-xl p-8 border border-lumen-yellow/20 shadow-lg space-y-8">
                
                <div>
                  <h3 className="text-2xl font-bold text-lumen-charcoal mb-4">1. What Are Cookies</h3>
                  <p className="text-lumen-mid-gray leading-relaxed">
                    Cookies are small text files that are stored on your device when you visit our website. They help us provide you with a better browsing experience and allow certain features to function properly.
                  </p>
                </div>

                <div>
                  <h3 className="text-2xl font-bold text-lumen-charcoal mb-4">2. Types of Cookies We Use</h3>
                  <div className="space-y-4">
                    <div>
                      <h4 className="text-lg font-semibold text-lumen-charcoal mb-2">Essential Cookies</h4>
                      <p className="text-lumen-mid-gray leading-relaxed">
                        These cookies are necessary for the website to function and cannot be switched off. They are usually set in response to actions you take.
                      </p>
                    </div>
                    <div>
                      <h4 className="text-lg font-semibold text-lumen-charcoal mb-2">Analytics Cookies</h4>
                      <p className="text-lumen-mid-gray leading-relaxed">
                        These cookies help us understand how visitors interact with our website by collecting and reporting information anonymously.
                      </p>
                    </div>
                    <div>
                      <h4 className="text-lg font-semibold text-lumen-charcoal mb-2">Functional Cookies</h4>
                      <p className="text-lumen-mid-gray leading-relaxed">
                        These cookies enable enhanced functionality and personalization, such as remembering your preferences.
                      </p>
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className="text-2xl font-bold text-lumen-charcoal mb-4">3. Managing Cookies</h3>
                  <p className="text-lumen-mid-gray leading-relaxed">
                    You can control and manage cookies through your browser settings. Most browsers allow you to block or delete cookies, though this may affect your experience on our website.
                  </p>
                </div>

                <div>
                  <h3 className="text-2xl font-bold text-lumen-charcoal mb-4">4. Third-Party Cookies</h3>
                  <p className="text-lumen-mid-gray leading-relaxed">
                    We may use third-party services that set their own cookies. These services help us analyze website traffic and improve our services. We do not control these third-party cookies.
                  </p>
                </div>

                <div>
                  <h3 className="text-2xl font-bold text-lumen-charcoal mb-4">5. Updates to This Policy</h3>
                  <p className="text-lumen-mid-gray leading-relaxed">
                    We may update this Cookie Policy from time to time. Any changes will be posted on this page with an updated revision date.
                  </p>
                </div>

                <div>
                  <h3 className="text-2xl font-bold text-lumen-charcoal mb-4">6. Contact Us</h3>
                  <p className="text-lumen-mid-gray leading-relaxed">
                    If you have any questions about our use of cookies, please contact <NAME_EMAIL>.
                  </p>
                </div>

                <div className="border-t border-lumen-yellow/20 pt-6">
                  <p className="text-sm text-lumen-mid-gray">
                    Last updated: January 2025
                  </p>
                </div>

              </div>
            </div>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default CookiePolicy;
