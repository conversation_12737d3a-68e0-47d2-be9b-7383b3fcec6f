import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { useState, useEffect } from "react";

const Cases = () => {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [isHovering, setIsHovering] = useState(false);

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      const heroSection = document.getElementById('cases-hero-section');
      if (heroSection) {
        const rect = heroSection.getBoundingClientRect();
        setMousePosition({
          x: e.clientX - rect.left,
          y: e.clientY - rect.top
        });
      }
    };

    const heroSection = document.getElementById('cases-hero-section');
    if (heroSection) {
      heroSection.addEventListener('mousemove', handleMouseMove);
      return () => heroSection.removeEventListener('mousemove', handleMouseMove);
    }
  }, []);
  const cases = [
    {
      title: "E-commerce Platform Overhaul",
      client: "RetailCorp",
      challenge: "Slow website causing 40% cart abandonment",
      solution: "Complete platform rebuild with performance optimization",
      results: [
        "Page load time reduced from 8s to 0.7s",
        "Cart abandonment down 65%",
        "Revenue increased 120%",
      ],
      industry: "Retail",
    },
    {
      title: "Cloud Migration & Scaling",
      client: "TechStartup Inc",
      challenge: "Infrastructure couldn't handle rapid user growth",
      solution: "Cloud-native architecture with auto-scaling",
      results: [
        "99.99% uptime achieved",
        "Handled 10x traffic increase",
        "Infrastructure costs reduced 40%",
      ],
      industry: "Technology",
    },
    {
      title: "Email Deliverability Recovery",
      client: "MarketingPro",
      challenge: "70% of emails going to spam folders",
      solution: "Email infrastructure rebuild and reputation management",
      results: [
        "Inbox delivery rate: 99.8%",
        "Open rates increased 300%",
        "Customer engagement up 250%",
      ],
      industry: "Marketing",
    },
    {
      title: "Process Automation System",
      client: "ManufacturingCorp",
      challenge: "Manual inventory management causing errors",
      solution: "Custom automation platform with real-time tracking",
      results: [
        "Manual tasks reduced 80%",
        "Inventory errors down 95%",
        "Staff productivity up 150%",
      ],
      industry: "Manufacturing",
    },
  ];

  return (
    <div className="min-h-screen bg-white">
      <Header />
      <main className="pt-16 lg:pt-20">

        <section
          id="cases-hero-section"
          onMouseEnter={() => setIsHovering(true)}
          onMouseLeave={() => setIsHovering(false)}
          className="w-full bg-gradient-to-br from-lumen-yellow via-lumen-yellow/95 to-lumen-yellow-hover py-20 lg:py-28 pt-28 relative overflow-hidden"
        >
          {/* Mouse-following gradient cloud */}
          {isHovering && (
            <div
              className="absolute w-96 h-96 bg-gradient-to-br from-white/30 to-lumen-yellow-hover/20 rounded-full blur-3xl pointer-events-none transition-all duration-500 ease-out"
              style={{
                left: mousePosition.x - 192,
                top: mousePosition.y - 192
              }}
            />
          )}

         
          {/* Background pattern overlay */}
          <div className="absolute inset-0 bg-gradient-to-br from-transparent via-lumen-yellow/10 to-lumen-yellow-hover/20"></div>

          <div className="max-w-content mx-auto px-6 relative z-10">
            <div className="text-center mb-8">
              <div className="inline-block px-4 py-2 bg-white/20 rounded-full border border-white/30 backdrop-blur-sm mb-6">
                <span className="text-sm font-medium text-lumen-charcoal">📈 Real results from real businesses</span>
              </div>
              <h2 className="text-4xl lg:text-5xl font-bold leading-tight xl:text-6xl mb-6">
                <span className="text-lumen-charcoal">Our recent work</span>
               
              </h2>
              <p className="text-lg text-lumen-charcoal/80 max-w-2xl mx-auto leading-relaxed">
                Real results from real businesses. See how we've helped companies transform their technology.
              </p>
            </div>
          </div>
        </section>
        <div className="py-20 lg:py-28 pt-20">
          <div className="max-w-content mx-auto px-6">

            <div className="grid lg:grid-cols-2 gap-12">
              {cases.map((caseStudy, index) => (
                <div
                  key={index}
                  className="bg-white rounded-xl p-8 border border-lumen-yellow/20 shadow-lg hover:shadow-2xl transition-all duration-300"
                >
                  <div className="flex justify-between items-start mb-6">
                    <div>
                      <h3 className="text-2xl font-bold text-lumen-charcoal mb-2">
                        {caseStudy.title}
                      </h3>
                      <p className="text-lumen-mid-gray">{caseStudy.client}</p>
                    </div>
                    <span className="px-3 py-1 bg-lumen-yellow/20 text-lumen-charcoal text-xs font-medium rounded-full">
                      {caseStudy.industry}
                    </span>
                  </div>

                  <div className="space-y-4">
                    <div>
                      <h4 className="font-semibold text-lumen-charcoal mb-2">
                        Challenge
                      </h4>
                      <p className="text-lumen-mid-gray text-sm">
                        {caseStudy.challenge}
                      </p>
                    </div>

                    <div>
                      <h4 className="font-semibold text-lumen-charcoal mb-2">
                        Solution
                      </h4>
                      <p className="text-lumen-mid-gray text-sm">
                        {caseStudy.solution}
                      </p>
                    </div>


                    <div>
                      <h4 className="font-semibold text-lumen-charcoal mb-2">
                        Results
                      </h4>
                      <ul className="space-y-1">
                        {caseStudy.results.map((result, idx) => (
                          <li
                            key={idx}
                            className="text-lumen-mid-gray text-sm flex items-center"
                          >
                            <span className="w-2 h-2 bg-lumen-yellow rounded-full mr-2"></span>
                            {result}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default Cases;
