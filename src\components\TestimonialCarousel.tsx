
import { useState, useEffect } from "react";

const TestimonialCarousel = () => {
  const [currentIndex, setCurrentIndex] = useState(0);
  
  const testimonials = [
    {
      quote: "LumenWorks cut our website loading time by 70%. Our customers actually complete purchases now instead of giving up halfway through.",
      author: "<PERSON>",
      company: "Artisan Coffee Co.",
      size: "25 employees"
    },
    {
      quote: "They explained everything in plain English and delivered exactly what they promised. No surprises, no tech jargon, just results.",
      author: "<PERSON>", 
      company: "Rodriguez Legal",
      size: "12 employees"
    },
    {
      quote: "Best tech investment we've made. Their 30-minute response time saved us during a critical system issue last month.",
      author: "<PERSON>",
      company: "Coastal Manufacturing",
      size: "85 employees"
    }
  ];

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentIndex((prevIndex) => (prevIndex + 1) % testimonials.length);
    }, 5000);
    return () => clearInterval(timer);
  }, [testimonials.length]);

  return (
    <section className="w-full bg-white py-16 lg:py-20">
      <div className="max-w-content mx-auto px-6">
        <div className="text-center mb-12">
          <h2 className="text-3xl lg:text-4xl font-bold text-lumen-charcoal section-beam mb-6">
            What business owners say
          </h2>
        </div>
        
        <div className="max-w-4xl mx-auto">
          <div className="relative overflow-hidden">
            <div 
              className="flex transition-transform duration-500 ease-in-out"
              style={{ transform: `translateX(-${currentIndex * 100}%)` }}
            >
              {testimonials.map((testimonial, index) => (
                <div key={index} className="w-full flex-shrink-0 px-4">
                  <div className="text-center">
                    <blockquote className="text-xl lg:text-2xl text-lumen-charcoal mb-6 leading-relaxed">
                      "{testimonial.quote}"
                    </blockquote>
                    <div className="space-y-1">
                      <div className="font-bold text-lumen-charcoal">
                        {testimonial.author}
                      </div>
                      <div className="text-lumen-mid-gray">
                        {testimonial.company} • {testimonial.size}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
          
          {/* Dots indicator */}
          <div className="flex justify-center space-x-2 mt-8">
            {testimonials.map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentIndex(index)}
                className={`w-3 h-3 rounded-full transition-colors ${
                  index === currentIndex ? 'bg-lumen-yellow' : 'bg-lumen-mid-gray/30'
                }`}
                aria-label={`Go to testimonial ${index + 1}`}
              />
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default TestimonialCarousel;
