
const ClientCarousel = () => {
  const clients = [
    { name: "<PERSON><PERSON><PERSON>", logo: "TF" },
    { name: "DataSync", logo: "<PERSON>" },
    { name: "CloudV<PERSON>", logo: "C<PERSON>" },
    { name: "<PERSON><PERSON><PERSON>", logo: "NC" },
    { name: "InfoBridge", logo: "I<PERSON>" },
    { name: "SystemX", logo: "SX" },
    { name: "DevHub", logo: "DH" },
    { name: "CodeB<PERSON>", logo: "CB" }
  ];

  return (
    <section className="w-full bg-white py-12 lg:py-16 overflow-hidden">
      <div className="max-w-content mx-auto px-6">
        <div className="text-center mb-8">
          <p className="text-lumen-mid-gray font-medium">Trusted by forward-thinking companies</p>
        </div>
        
        <div className="relative">
          <div className="flex animate-scroll">
            {/* First set of logos */}
            {clients.map((client, index) => (
              <div
                key={`first-${index}`}
                className="flex items-center justify-center min-w-[200px] mx-8"
              >
                <div className="w-12 h-12 bg-gradient-to-br from-lumen-yellow/20 to-lumen-yellow/40 rounded-xl flex items-center justify-center mr-3">
                  <span className="font-mono font-bold text-lumen-charcoal text-sm">
                    {client.logo}
                  </span>
                </div>
                <span className="text-lumen-charcoal font-medium">{client.name}</span>
              </div>
            ))}
            {/* Duplicate set for seamless loop */}
            {clients.map((client, index) => (
              <div
                key={`second-${index}`}
                className="flex items-center justify-center min-w-[200px] mx-8"
              >
                <div className="w-12 h-12 bg-gradient-to-br from-lumen-yellow/20 to-lumen-yellow/40 rounded-xl flex items-center justify-center mr-3">
                  <span className="font-mono font-bold text-lumen-charcoal text-sm">
                    {client.logo}
                  </span>
                </div>
                <span className="text-lumen-charcoal font-medium">{client.name}</span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default ClientCarousel;
