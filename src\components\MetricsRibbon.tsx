
const MetricsRibbon = () => {
  const metrics = [
    { value: "99.9%", label: "uptime" },
    { value: "<1s", label: "page-load" },
    { value: "99.99%", label: "email deliverability" },
    { value: "24/7", label: "support" }
  ];

  return (
    <section className="w-full bg-gradient-to-r from-lumen-off-white via-white to-lumen-off-white py-12 lg:py-16 relative">
      <div className="absolute inset-0 bg-gradient-to-r from-lumen-yellow/5 via-transparent to-lumen-yellow/5"></div>
      <div className="max-w-content mx-auto px-6 relative z-10">
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
          {metrics.map((metric, index) => (
            <div key={index} className="text-center group">
              <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 border border-lumen-yellow/20 shadow-lg hover:shadow-xl transform hover:-translate-y-2 transition-all duration-300 group-hover:border-lumen-yellow/40">
                <div className="text-3xl lg:text-4xl font-mono font-bold text-lumen-charcoal mb-2 bg-gradient-to-br from-lumen-charcoal to-lumen-mid-gray bg-clip-text text-transparent">
                  {metric.value}
                </div>
                <div className="text-sm text-lumen-mid-gray uppercase tracking-wide font-medium">
                  {metric.label}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default MetricsRibbon;
