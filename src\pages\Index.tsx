import Header from "@/components/Header";
import Hero from "@/components/Hero";
import MetricsRibbon from "@/components/MetricsRibbon";
import BrandStory from "@/components/BrandStory";
import ServiceGrid from "@/components/ServiceGrid";
import ClientCarousel from "@/components/ClientCarousel";
import TestimonialCarousel from "@/components/TestimonialCarousel";
import CTAStripe from "@/components/CTAStripe";
import Footer from "@/components/Footer";
import CasesSection from "@/components/CasesSection";

const Index = () => {
  return (
    <div className="min-h-screen bg-white relative overflow-hidden">
      {/* Gradient Cloud Background */}
      <div className="fixed inset-0 pointer-events-none">
        <div className="absolute top-0 left-1/4 w-96 h-96 bg-gradient-to-br from-lumen-yellow/10 to-white/50 rounded-full blur-3xl transform -translate-y-1/2"></div>
        <div className="absolute top-1/3 right-1/4 w-80 h-80 bg-gradient-to-bl from-lumen-yellow/15 to-lumen-yellow/5 rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/4 left-1/3 w-72 h-72 bg-gradient-to-tr from-lumen-yellow/8 to-white/30 rounded-full blur-3xl"></div>
        <div className="absolute top-2/3 right-1/3 w-64 h-64 bg-gradient-to-tl from-lumen-yellow/12 to-white/40 rounded-full blur-3xl"></div>
      </div>
      
      {/* Content */}
      <div className="relative z-10">
        <Header />
        <main className="pt-16 lg:pt-20">
          <Hero />
          <MetricsRibbon />
          <BrandStory />
          <ServiceGrid />
          <CasesSection />
          <ClientCarousel />
          <TestimonialCarousel />
          <CTAStripe />
        </main>
        <Footer />
      </div>
    </div>
  );
};

export default Index;
